
import requests
import time
import json

class WeCom:
    def __init__(self, corpid, corpsecret, agentid):
        self.corpid = corpid
        self.corpsecret = corpsecret
        self.agentid = agentid
        self.access_token = None
        self.token_expires_at = 0

    def get_access_token(self):
        if time.time() < self.token_expires_at:
            return self.access_token

        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.corpid}&corpsecret={self.corpsecret}"
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
            if "access_token" in data:
                self.access_token = data["access_token"]
                # expires_in is in seconds, give it a 60 second buffer
                self.token_expires_at = time.time() + data["expires_in"] - 60
                return self.access_token
            else:
                raise Exception(f"Failed to get access token: {data}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {e}")

    def send_text_message(self, content, touser="@all", toparty=None, totag=None):
        token = self.get_access_token()
        if not token:
            raise Exception("Could not get access token")

        url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={token}"
        
        payload = {
            "agentid": self.agentid,
            "msgtype": "text",
            "text": {
                "content": content
            },
            "safe": 0
        }

        if touser:
            payload["touser"] = touser
        if toparty:
            payload["toparty"] = toparty
        if totag:
            payload["totag"] = totag
        
        try:
            response = requests.post(url, data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {e}")

if __name__ == '__main__':
    # Replace with your actual credentials
    CORP_ID = "YOUR_CORP_ID"
    CORP_SECRET = "YOUR_CORP_SECRET"
    AGENT_ID = "YOUR_AGENT_ID"

    wecom = WeCom(CORP_ID, CORP_SECRET, AGENT_ID)
    
    try:
        # Example: send a message to everyone
        response = wecom.send_text_message("This is a test message from the script.")
        print("Message sent successfully:", response)
        
        # Example: send a message to a specific user
        # response = wecom.send_text_message("This is a private message.", touser="USER_ID")
        # print("Message sent successfully:", response)

    except Exception as e:
        print(f"An error occurred: {e}")
