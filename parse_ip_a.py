
#!/usr/bin/env python3
import re
import json

def get_interface_type(name):
    # Common prefixes for physical interfaces (Ethernet, WLAN, WWAN)
    physical_prefixes = ("eth", "en", "wl", "ww")

    if name == "lo":
        return "loopback"
    if name.startswith(physical_prefixes):
        return "physical"
    if name.startswith(("veth", "docker", "br-")) or "@" in name:
        return "virtual"
    return "unknown"

def parse_ip_a(output):
    interfaces = []
    current_interface = None
    for line in output.splitlines():
        if line.strip() == "":
            continue

        # Check for interface line
        match = re.match(r'^(\d+):\s+([\w@-]+):\s+<(.+)>', line)
        if match:
            if current_interface:
                interfaces.append(current_interface)
            interface_name = match.group(2)
            current_interface = {
                "name": interface_name,
                "type": get_interface_type(interface_name),
                "flags": match.group(3).split(','),
                "ip_address": None,
                "ipv6_address": None,
                "mac_address": None
            }
            continue

        # Check for inet line (IP address)
        match = re.search(r'inet\s+([\d\.]+/\d+)', line)
        if match and current_interface:
            current_interface["ip_address"] = match.group(1)
            continue

        # Check for inet6 line (IPv6 address)
        match = re.search(r'inet6\s+([a-fA-F0-9:]+/\d+)', line)
        if match and current_interface:
            new_address = match.group(1)
            current_address = current_interface.get("ipv6_address")

            # Prioritize global addresses over link-local ones.
            # A global address does not start with 'fe80:'.
            is_new_global = not new_address.startswith("fe80:")
            is_current_global = current_address and not current_address.startswith("fe80:")

            # Store the new address if:
            # 1. We don't have an address yet.
            # 2. The new one is global and the current one is not.
            if not current_address or (is_new_global and not is_current_global):
                current_interface["ipv6_address"] = new_address
            continue

        # Check for link/ether line (MAC address)
        match = re.search(r'link/ether\s+([\da-fA-F:]+)', line)
        if match and current_interface:
            current_interface["mac_address"] = match.group(1)
            continue

    if current_interface:
        interfaces.append(current_interface)

    return interfaces

if __name__ == "__main__":
    import sys
    input_data = sys.stdin.read()
    parsed_data = parse_ip_a(input_data)
    print(json.dumps(parsed_data, indent=4))
