
import os
import sys
import json
import argparse
import requests

# --- API Configuration ---
API_BASE_URL = "https://api.cloudflare.com/client/v4"

class CloudflareAPI:
    """
    A wrapper for the Cloudflare API to manage DNS records.
    """
    def __init__(self, api_token, zone_name):
        if not api_token or not zone_name:
            print("Error: CLOUDFLARE_API_TOKEN and CLOUDFLARE_ZONE_NAME environment variables must be set.", file=sys.stderr)
            sys.exit(1)
            
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json"
        }
        self.zone_name = zone_name
        self.zone_id = self._get_zone_id()

    def _get_zone_id(self):
        """Retrieves the Zone ID for the given zone name."""
        url = f"{API_BASE_URL}/zones"
        params = {"name": self.zone_name}
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            data = response.json()
            if data.get("result"):
                return data["result"][0]["id"]
            else:
                print(f"Error: Zone '{self.zone_name}' not found.", file=sys.stderr)
                sys.exit(1)
        except requests.exceptions.RequestException as e:
            print(f"Error fetching Zone ID: {e}", file=sys.stderr)
            print(f"Response: {e.response.text}", file=sys.stderr)
            sys.exit(1)

    def list_records(self, record_type=None, name=None):
        """Lists DNS records, with optional filtering by type and name."""
        url = f"{API_BASE_URL}/zones/{self.zone_id}/dns_records"
        params = {}
        if record_type:
            params['type'] = record_type
        if name:
            params['name'] = name
            
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error listing records: {e}", file=sys.stderr)
            print(f"Response: {e.response.text}", file=sys.stderr)
            return None

    def create_record(self, record_type, name, content, ttl=3600, proxied=False):
        """Creates a new DNS record."""
        url = f"{API_BASE_URL}/zones/{self.zone_id}/dns_records"
        data = {
            "type": record_type,
            "name": name,
            "content": content,
            "ttl": ttl,
            "proxied": proxied
        }
        try:
            response = requests.post(url, headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error creating record: {e}", file=sys.stderr)
            print(f"Response: {e.response.text}", file=sys.stderr)
            return None

    def update_record(self, record_id, record_type, name, content, ttl=3600, proxied=False):
        """Updates an existing DNS record."""
        url = f"{API_BASE_URL}/zones/{self.zone_id}/dns_records/{record_id}"
        data = {
            "type": record_type,
            "name": name,
            "content": content,
            "ttl": ttl,
            "proxied": proxied
        }
        try:
            response = requests.put(url, headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error updating record: {e}", file=sys.stderr)
            print(f"Response: {e.response.text}", file=sys.stderr)
            return None

    def delete_record(self, record_id):
        """Deletes a DNS record."""
        url = f"{API_BASE_URL}/zones/{self.zone_id}/dns_records/{record_id}"
        try:
            response = requests.delete(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error deleting record: {e}", file=sys.stderr)
            print(f"Response: {e.response.text}", file=sys.stderr)
            return None

def main():
    """Main function to parse arguments and execute commands."""
    parser = argparse.ArgumentParser(description="Manage Cloudflare DNS records via API.")
    subparsers = parser.add_subparsers(dest="command", required=True)

    # --- List Command ---
    parser_list = subparsers.add_parser("list", help="List DNS records.")
    parser_list.add_argument("--type", help="Filter by record type (e.g., A, CNAME).")
    parser_list.add_argument("--name", help="Filter by record name.")

    # --- Create Command ---
    parser_create = subparsers.add_parser("create", help="Create a new DNS record.")
    parser_create.add_argument("type", help="Record type (e.g., A, CNAME, TXT).")
    parser_create.add_argument("name", help="Record name (e.g., 'subdomain.example.com').")
    parser_create.add_argument("content", help="Record content (e.g., IP address or another domain).")
    parser_create.add_argument("--ttl", type=int, default=3600, help="Time To Live in seconds.")
    parser_create.add_argument("--proxied", action="store_true", help="Enable Cloudflare proxy.")

    # --- Update Command ---
    parser_update = subparsers.add_parser("update", help="Update an existing DNS record.")
    parser_update.add_argument("id", help="The ID of the record to update.")
    parser_update.add_argument("type", help="New record type.")
    parser_update.add_argument("name", help="New record name.")
    parser_update.add_argument("content", help="New record content.")
    parser_update.add_argument("--ttl", type=int, default=3600, help="New TTL in seconds.")
    parser_update.add_argument("--proxied", action="store_true", help="Set Cloudflare proxy status.")

    # --- Delete Command ---
    parser_delete = subparsers.add_parser("delete", help="Delete a DNS record.")
    parser_delete.add_argument("id", help="The ID of the record to delete.")

    args = parser.parse_args()

    api = CloudflareAPI(
        api_token=os.getenv("CLOUDFLARE_API_TOKEN"),
        zone_name=os.getenv("CLOUDFLARE_ZONE_NAME")
    )

    result = None
    if args.command == "list":
        result = api.list_records(record_type=args.type, name=args.name)
    elif args.command == "create":
        result = api.create_record(args.type, args.name, args.content, args.ttl, args.proxied)
    elif args.command == "update":
        result = api.update_record(args.id, args.type, args.name, args.content, args.ttl, args.proxied)
    elif args.command == "delete":
        result = api.delete_record(args.id)

    if result:
        print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
