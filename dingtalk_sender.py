
import requests
import json

class DingTalkBot:
    def __init__(self, webhook):
        self.webhook = webhook

    def send_text_message(self, content, at_mobiles=None, is_at_all=False):
        headers = {
            'Content-Type': 'application/json'
        }
        payload = {
            "msgtype": "text",
            "text": {
                "content": content
            },
            "at": {}
        }
        if at_mobiles:
            payload["at"]["atMobiles"] = at_mobiles
        if is_at_all:
            payload["at"]["isAtAll"] = is_at_all

        try:
            response = requests.post(self.webhook, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {e}")

if __name__ == '__main__':
    # Replace with your actual webhook URL
    WEBHOOK_URL = "YOUR_DINGTALK_WEBHOOK_URL"

    bot = DingTalkBot(WEBHOOK_URL)

    try:
        # Example: send a simple text message
        response = bot.send_text_message("Hello from the DingTalk bot!")
        print("Message sent successfully:", response)

        # Example: send a message and @ specific people
        # response = bot.send_text_message("This is a message for specific people.", at_mobiles=['MOBILE_PHONE_NUMBER_1', 'MOBILE_PHONE_NUMBER_2'])
        # print("Message sent successfully:", response)

        # Example: send a message and @ everyone
        # response = bot.send_text_message("This is a message for everyone in the group.", is_at_all=True)
        # print("Message sent successfully:", response)

    except Exception as e:
        print(f"An error occurred: {e}")
